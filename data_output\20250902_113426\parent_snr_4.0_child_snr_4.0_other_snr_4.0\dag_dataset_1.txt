parents: ['X1', 'X3', 'X10'] -> child: X0
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.11966192607214012

parents: ['X18'] -> child: X5
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.028140186847114505

parents: ['X1'] -> child: X6
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.19297119633286142

parents: ['X18'] -> child: X7
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.41306667418238774

parents: ['X5', 'X14', 'X16'] -> child: X8
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.18774189842284095

parents: ['X1', 'X2', 'X4', 'X11', 'X13', 'X17', 'X18'] -> child: X9
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.3565279303132111

parents: ['X1'] -> child: X12
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.33329740221360005

parents: ['X12'] -> child: X15
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.03202330909548774

parents: ['X5'] -> child: X19
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.024917530122781376