parents: ['X1', 'X3', 'X10'] -> child: X0
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.16922751875090722

parents: ['X18'] -> child: X5
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.039796233886902316

parents: ['X1'] -> child: X6
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.2729024830012939

parents: ['X18'] -> child: X7
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.5841644927930811

parents: ['X5', 'X14', 'X16'] -> child: X8
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.8396107550056229

parents: ['X1', 'X2', 'X4', 'X11', 'X13', 'X17', 'X18'] -> child: X9
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.5042066344137529

parents: ['X1'] -> child: X12
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.4713537065141936

parents: ['X12'] -> child: X15
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.046759214053584144

parents: ['X5'] -> child: X19
  f函数:

  g函数: identity
  init_std: 3.733651643995085
  noise_std: 0.12236745907896396