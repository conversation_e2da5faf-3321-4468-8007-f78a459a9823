parents: ['X9'] -> child: X3
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.008052548878546725

parents: ['X0', 'X2', 'X4', 'X5', 'X8'] -> child: X6
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.07764995809947399

parents: ['X7'] -> child: X8
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.025591729005512386

parents: ['X0'] -> child: X9
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.5541359953590782

parents: ['X7'] -> child: X10
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.41422121586851685

parents: ['X0', 'X1'] -> child: X11
  f函数:

  g函数: identity
  init_std: 2.5843593226069403
  noise_std: 0.6300059353458014